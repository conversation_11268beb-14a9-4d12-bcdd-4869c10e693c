<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevoProducto.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevoProducto" %>


<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="package" class="me-2"></i>Nuevo producto</h4>
                <h6>Crear nuevo producto</h6>
            </div>
        </div>
        <ul class="table-top-head">
            <li>
                <div class="page-btn">
                    <a href="Default.aspx" class="btn btn-secondary" title="Regresar al listado de produtos">
                        <i data-feather="arrow-left" class="me-2"></i>
                    </a>
                </div>
            </li>
            <li>
                <a data-bs-toggle="tooltip" data-bs-placement="top" title="Collapse" id="collapse-header">
                    <i data-feather="chevron-up" class="feather-chevron-up"></i>
                </a>
            </li>
        </ul>
    </div>

    <%-- DETALLE --%>
    <form id="frm-save-product" method="post">
    <div class="form-horizontal">
        <div class="row">
            <div class="col-md-12">
                <%-- INFORMACION DEL PRODUCTO --%>

                <div class="iq-card card " style="padding: 10px;">
                    <div class="iq-card-header d-flex justify-content-between">
                        <div class="iq-header-title">
                            <h4 class="card-title">Información de producto</h4>
                        </div>
                    </div>
                    <div class="iq-card-body">
                        <div class="row">

                            <div class="col-md-2">

                                <div class="container-image">
                                    <div class="hover">
                                        <p>Carga imagen</p>
                                    </div>
                                    <%-- @if (string.IsNullOrWhiteSpace(Model.img_producto))
                                {
                                    <img id="img_producto" src="data:image/png;base64,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" />
                                }
                                else
                                {
                                    <img id="img_producto" name="img_producto" src="@Model.img_producto" />
                                }--%>
                                    <img style="cursor: pointer;" id="img_producto" src="data:image/png;base64,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" />

                                    <input id="file_product" name="file_product" type="file" style="display: none;" accept="image/*" />
                                </div>

                                <%--<h4 id="lbl_barcode" class="barcode">&nbsp;</h4>--%>
                            </div>

                            <div class="col-md-10">

                                <div class="form-horizontal">

                                    <%-- ID PRODUCTO --%>
                                    <div class="form-group row" style="display: none;">
                                        <label for="id_producto" class="control-label col-md-12">ID Producto</label>
                                        <div class="col-md-12">
                                            <input type="text" id="id_producto" name="id_producto" class="form-control form-control-sm" />
                                            <span id="id_producto-validation" class="text-danger"></span>
                                        </div>
                                    </div>


                                    <div class="row">

                                        <%-- CODIGO --%>
                                        <div class="col-md-6">

                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-barcode"></i>&nbsp;
                                                <label for="codigo">Código</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <div class="input-group input-group-sm">
                                                        <input type="text" id="codigo" name="codigo" class="form-control input-xs" placeholder="Código de barras" search="0" />
                                                        <span class="text-danger" id="codigo-validation"></span>

                                                        <div class="input-group-append">
                                                            <button id="btnBusProCod" class="btn btn-success" type="button" tabindex="-1">
                                                                <i class="fa fa-search"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-car"></i>&nbsp;
                                            <label for="id_marca">Marca</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <select id="id_marca" name="id_marca" class="form-control form-select">
                                                        <option value="">Seleccionar marca</option>

                                                    </select>
                                                    <span class="text-danger" id="id_marca-validation"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-user-tie"></i>&nbsp;
                                            <label for="id_proveedor">Proveedor</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <select id="id_proveedor" name="id_proveedor" class="form-control form-select">
                                                        <option value="">Seleccionar proveedor</option>
                                                        <!-- Datos falsos para el dropdown -->
                                                        <option value="1">Proveedor A</option>
                                                        <option value="2">Proveedor B</option>
                                                    </select>
                                                    <span class="text-danger" id="id_proveedor-validation"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-car"></i>&nbsp;
                                        <label for="id_modelo">Modelo</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <select id="id_modelo" name="id_modelo" class="form-control form-select">
                                                        <option value="">Seleccionar modelo</option>
                                                        <!-- Datos falsos para el dropdown -->
                                                        <option value="1">Modelo A</option>
                                                        <option value="2">Modelo B</option>
                                                    </select>
                                                    <span class="text-danger" id="id_modelo-validation"></span>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-box"></i>&nbsp;
            <label for="id_categoria">Categoría</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <select id="id_categoria" name="id_categoria" class="form-control form-select">
                                                        <option value="">Seleccionar categoría</option>
                                                        <!-- Datos falsos para el dropdown -->
                                                        <option value="1">Electrónica</option>
                                                        <option value="2">Ropa</option>
                                                        <option value="3">Alimentos</option>
                                                        <option value="4">Hogar</option>
                                                        <option value="5">Juguetes</option>
                                                    </select>
                                                    <span class="text-danger" id="id_categoria-validation"></span>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="fa fa-calendar"></i>&nbsp;
            <label for="anio">Año</label>
                                                </div>
                                                <div class="col-sm-8">
                                                    <select id="anio" name="anio" class="form-control form-control-sm form-select">
                                                        <option value="">Seleccionar año</option>
                                                        <!-- Datos falsos para el dropdown de años -->
                                                        <option value="2020">2020</option>
                                                        <option value="2021">2021</option>
                                                    </select>
                                                    <span class="text-danger" id="anio-validation"></span>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>


                            </div>

                            <div class="row"></div>

                            <div class="col-md-12">
                                <!-- NOMBRE -->
                                <div class="form-group row">
                                    <div class="col-sm-2">
                                        <i class="fa fa-box"></i>&nbsp;
                                    <label for="nombre">Nombre</label>
                                    </div>
                                    <div class="col-sm-10">
                                        <input type="text" id="nombre" name="nombre" class="form-control form-control-sm" placeholder="Producto" required="required" />
                                        <span class="text-danger" id="nombre-validation"></span>
                                    </div>
                                </div>

                                <!-- DESCRIPCIÓN -->
                                <div class="form-group row">
                                    <div class="col-sm-2">
                                        <i class="fa fa-boxes"></i>&nbsp;
                                    <label for="descripcion">Descripción</label>
                                    </div>
                                    <div class="col-sm-10">
                                        <textarea id="descripcion" name="descripcion" class="form-control" placeholder="Descripción" rows="2" style="resize: none;" required="required"></textarea>
                                        <span class="text-danger" id="descripcion-validation"></span>
                                    </div>
                                </div>

                                <br />
                            </div>

                        </div>
                    </div>
                </div>


                <div class="row"></div>
                <div class="row g-3">

                    <div class="col-md-6 card" style="padding: 10px;">

                        <div class="iq-card">
                            <div class="iq-card-header d-flex justify-content-between">
                                <div class="iq-header-title">
                                    <h4 class="card-title"><i class="fa fa-bags-shopping"></i>&nbsp;Detalle de compra</h4>
                                </div>
                            </div>
                            <div class="iq-card-body">

                                <!-- referencia_compra -->
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-file-invoice"></i>&nbsp;
                                            <label for="referencia_compra" class="control-label">Referencia de Compra</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="text" id="referencia_compra" name="referencia_compra" class="form-control form-control-sm" placeholder="Cod. Referencia" value="REF12345" />
                                        </div>
                                    </div>
                                </div>

                                <!-- fecha_compra -->
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-calendar"></i>&nbsp;
                                            <label for="fecha_compra" class="control-label">Fecha de Compra</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="date" id="fecha_compra" name="fecha_compra" class="form-control form-control-sm" value="2025-04-06" />
                                        </div>
                                    </div>
                                </div>
                                <!-- fecha_vencimiento -->
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-calendar"></i>&nbsp;
                                            <label for="fecha_vencimiento" class="control-label">Fecha de Vencimiento</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="date" id="fecha_vencimiento" name="fecha_vencimiento" class="form-control form-control-sm" value="2025-12-31" />
                                        </div>
                                    </div>
                                </div>
                                <br />

                                <!-- costo_unitario -->
                                <div class="form-horizontal">
                                    <!-- moneda -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-money-bill"></i>&nbsp;
                                            <label for="costo_unitario" class="control-label">Costo Unitario</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                <select id="id_moneda" name="id_moneda" class="form-control currency">
                                                    <option value="1">USD</option>
                                                    <option value="2">EUR</option>
                                                </select>
                                                <input type="number" id="costo_unitario" name="costo_unitario" class="form-control price" placeholder="Costo unitario" value="100" required="required" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- precio -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-money-bill"></i>&nbsp;
            <label for="precio_unitario" class="control-label">Precio Unitario</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                <select id="id_moneda_venta" name="id_moneda_venta" class="form-control currency id_moneda_venta" disabled="disabled">
                                                    <option value="1">USD</option>
                                                    <option value="2">EUR</option>
                                                </select>
                                                <input type="number" id="precio_unitario" name="precio_unitario" class="form-control price" placeholder="Precio venta" value="150" required="required" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- precio min -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-money-bill"></i>&nbsp;
            <label for="min_descuento" class="control-label">Precio Min. (descuento)</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                <select id="id_moneda_venta_1" name="id_moneda_venta_1" class="form-control currency id_moneda_venta" disabled="disabled">
                                                    <option value="1">USD</option>
                                                    <option value="2">EUR</option>
                                                </select>
                                                <input type="number" id="min_descuento" name="min_descuento" class="form-control price" placeholder="Precio min (descuento)" value="130" />
                                            </div>
                                        </div>
                                    </div>
                                    <!-- ganancia_producto -->
                                    <div class="form-group row">
                                        <div id="label-ganancia" class="col-sm-6">
                                            <i id="icon_ganancia" class="fa fa-coins"></i>&nbsp;
            <label for="ganancia_producto">Ganancia Producto</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                <select id="id_moneda_venta_2" name="id_moneda_venta_2" class="form-control currency id_moneda_venta" disabled="disabled">
                                                    <option value="1">USD</option>
                                                    <option value="2">EUR</option>
                                                </select>
                                                <input type="number" id="ganancia_producto" name="ganancia_producto" class="form-control price" placeholder="Ganancia por producto" value="50" disabled="disabled" />
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 card" style="padding: 10px;">
                        <div class="iq-card">
                            <div class="iq-card-header d-flex justify-content-between">
                                <div class="iq-header-title">
                                    <h4 class="card-title"><i class="fa fa-bags-shopping"></i>&nbsp;Stock inventario</h4>
                                </div>
                            </div>
                            <div class="iq-card-body">

                                <!-- stock -->
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-box-check"></i>&nbsp;
            <label class="control-label">Stock</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" id="stock" name="stock" class="form-control form-control-sm stock" step="any" required="required" />
                                        </div>
                                    </div>
                                </div>

                                <!-- stock_actual -->
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-boxes"></i>&nbsp;
            <label class="control-label">Stock Actual</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" id="stock_actual" name="stock_actual" class="form-control form-control-sm stock" disabled="disabled" step="any" />
                                        </div>
                                    </div>

                                    <!-- stock_total -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-boxes"></i>&nbsp;
            <label class="control-label">Stock Total</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" id="stock_total" name="stock_total" class="form-control form-control-sm stock" disabled="disabled" step="any" />
                                        </div>
                                    </div>

                                    <br />

                                    <!-- stock_maximo -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-bell-on txt-green"></i>&nbsp;
            <label class="control-label">Stock Máximo</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" id="stock_maximo" name="stock_maximo" class="form-control form-control-sm stock" placeholder="Stock máximo" step="any" />
                                        </div>
                                    </div>

                                    <!-- stock_minimo -->
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="fa fa-bell-on txt-green"></i>&nbsp;
            <label class="control-label">Stock Mínimo</label>
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="number" id="stock_minimo" name="stock_minimo" class="form-control form-control-sm stock" placeholder="Stock mínimo" step="any" />
                                        </div>
                                    </div>


                                    <br />
                                    <p></p>
                                    <br />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col text-center">
                <button id="btnGuardaProducto" type="button" class="btn btn-lg btn-success"><i class="far fa-save"></i>&nbsp;Guardar</button>&nbsp;
            <button id="btnCancelaProducto" type="button" class="btn btn-lg btn-danger" onclick="window.location.href = '/Inventario'"><i class="fa fa-times"></i>&nbsp;Cancelar</button>
            </div>
        </div>
        <br />
    </div>
    </form>



    <script type="text/javascript" src="<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/jquery-validate/jquery.validate.js"></script>
    <script type="text/javascript">
        ///




        function _findProduct(code) {
            var _data = {
                codigo: code
            }
            $.ajax({
                url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Producto/GetProductByCode",
            data: _data,
            type: "POST",
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                Holdon_Open("Procesando...");
            },
            success: function (response) {
                if (response.data) {
                    if (response.data.length > 0) {
                        let data_value = response.data[0];
                        setDataProduct(data_value);
                    }
                    else {
                        $("#codigo").attr("search", "0").attr("data-valor", "");
                        $("#id_producto").val("0");

                        //Limpiar los campos
                        //$("input[type=number], input[type=email], textarea, input[type=date]").val("");
                        limpiarCampos();
                    }
                } else {
                    $("#codigo").attr("search", "0").attr("data-valor", "");
                    $("#id_producto").val("0");
                    limpiarCampos();
                }
            },
            error: function (response) {
                $("#codigo").attr("search", "0");
            },
            complete: function (response) {
                Holdon_Close();
            }
        });
        }

        $(document).ready(function () {

            // Inicializar campos con valores por defecto
            limpiarCampos();

            // Cargar DDL al inicializar la página
            cargarProveedores();
            cargarCategorias();
            cargarMarcas();

            // Configurar validaciones para campos numéricos
            $(".form-control.stock, .form-control.price").each(function () {
                $(this).attr("min", $(this).val() || "0");
                $(this).attr("value", $(this).val() || "0");
            });

            // Validaciones específicas para campos de stock (solo números enteros)
            $(".form-control.stock").each(function () {
                $(this).on("keydown", function (event) {
                    if (event.key === '.') { event.preventDefault(); }
                });
                $(this).on("input", function (event) {
                    event.target.value = event.target.value.replace(/[^0-9]*/g, '');
                });
                $(this).on("blur", function () {
                    if ($(this).val().trim() === "") {
                        $(this).val($(this).val() || "0");
                    }
                });
            });

            // Cuando el usuario hace clic en la imagen
            document.getElementById("img_producto").addEventListener("click", function () {
                // Se dispara el clic del input file
                document.getElementById("file_product").click();
            });
























                        let select = $("#id_marca");
                        select.empty(); // Limpia opciones anteriores
                        select.append('<option value="">Seleccionar marca</option>');
                        response.data.forEach(function (item) {
                            select.append(`<option value="${item.id_marca}">${item.descripcion}</option>`);
                        });
                    }
                },
                error: function (xhr, status, error) {
                    console.log("Error cargando marcas:", error);
                },
                complete: function () {
                    //__ProgressOff();
                }
            });




            function registrarProducto() {
                var jsonData = {
                    id_producto: 0,
                    id_contribuyente: 0,
                    id_sucursal: 0,
                    id_categoria: 0,
                    categoria: "string",
                    id_marca: 0,
                    marca: "string",
                    id_modelo: 0,
                    modelo: "string",
                    anio: 0,
                    id_proveedor: 0,
                    proveedor: "string",
                    codigo: "string",
                    nombre: "string",
                    descripcion: "string",
                    img_producto: "string",
                    stock: 0,
                    id_moneda: 0,
                    id_moneda_venta: 0,
                    precio_unitario: 0,
                    min_descuento: 0,
                    ganancia_producto: 0,
                    fecha_compra: "2025-04-21T02:05:29.693Z",
                    referencia_compra: "string",
                    costo_unitario: 0,
                    stock_minimo: 0,
                    stock_maximo: 0,
                    fecha_vencimiento: "2025-04-21T02:05:29.693Z",
                    stock_actual: 0,
                    stock_total: 0,
                    fecha_regi: "2025-04-21T02:05:29.693Z",
                    fecha_actu: "2025-04-21T02:05:29.693Z",
                    id_usu_regi: 0,
                    id_usu_actu: 0,
                    estado: true
                };

                console.log("lo que va a ir");
                console.log(jsonData);

                var fmProducto = new FormData();
                fmProducto.append("mth", mtdEnc("crear/producto")); // Cambia el método según lo que necesites
                fmProducto.append("data", mtdEnc(JSON.stringify(jsonData)));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/inventario.ashx",
                    type: "post",
                    contentType: false,
                    processData: false,
                    dataType: "json",
                    data: fmProducto,
                    success: function (response) {
                        console.log("Producto registrado: ");
                        console.log(response);
                    },
                    complete: function () {
                        __ProgressOff();
                    }
                });
            }

















            ///









            //if (@Model.id_producto === 0)
            //{
            //    limpiarCampos();
            //}

            $(".form-control.stock, .form-control.price").each(function () {
                $(this).attr("min", $(this).val() || "0");
                $(this).attr("value", $(this).val() || "0");
            });

            $(".form-control.stock").each(function () {

                $(this).on("keydown", function (event) {
                    if (event.key === '.') { event.preventDefault(); }
                });
                $(this).on("input", function (event) {
                    event.target.value = event.target.value.replace(/[^0-9]*/g, '');
                });

                $(this).on("blur", function () {
                    if ($(this).val().trim() === "") {
                        $(this).val($(this).val() || "0");
                    }
                });
            });

            // Validaciones de cálculo de ganancia y precios
            $("#costo_unitario").on("keyup, change", function () {
                // Solo actualizar el atributo min del precio unitario, no su valor
                $("#precio_unitario").attr("min", $(this).val());
                $("#min_descuento").attr("min", $(this).val());

                // Recalcular ganancia si hay valores en ambos campos
                if ($("#precio_unitario").val()) {
                    $("#precio_unitario").trigger("change");
                }
            });

            $("#costo_unitario, #precio_unitario").on("keyup, change", function () {
                let precio = $("#precio_unitario").val() || 0;
                let costo_unitario = $("#costo_unitario").val() || 0;
                let ganancia = (precio - costo_unitario).toFixed(2);

                $("#icon_ganancia").attr("class", "");
                $("#label-ganancia").attr("class", "");

                if (ganancia >= 0) {
                    $("#icon_ganancia").addClass("far fa-angle-up");
                    $("#label-ganancia").addClass("text-success col-sm-6");
                } else {
                    Swal.fire({
                        icon: 'warning',
                        text: 'El Precio no debe ser menor al costo del producto.',
                    });
                    precio = costo_unitario;
                    ganancia = (precio - costo_unitario).toFixed(2);
                    $("#precio_unitario").val(precio);

                    $("#icon_ganancia").addClass("far fa-angle-down");
                    $("#label-ganancia").addClass("text-red col-sm-6");
                }

                $("#ganancia_producto").val(ganancia);

                // Solo actualizar los atributos min del precio mínimo, no su valor
                $("#min_descuento").attr("min", costo_unitario);
                $("#min_descuento").attr("max", precio);
            });

            // Validación para precio mínimo (descuento) - no debe ser menor al costo unitario
            $("#min_descuento").on("keyup, change", function () {
                let precio_min = parseFloat($(this).val()) || 0;
                let costo_unitario = parseFloat($("#costo_unitario").val()) || 0;
                let precio_unitario = parseFloat($("#precio_unitario").val()) || 0;

                if (precio_min < costo_unitario) {
                    Swal.fire({
                        icon: 'warning',
                        text: 'El Precio mínimo no debe ser menor al costo del producto.',
                    });
                    $(this).val(costo_unitario);
                } else if (precio_min > precio_unitario) {
                    Swal.fire({
                        icon: 'warning',
                        text: 'El Precio mínimo no debe ser mayor al precio de venta.',
                    });
                    $(this).val(precio_unitario);
                }
            });

            // Validación de stock y cálculo de stock total
            $("#stock").on("keyup, change", function () {
                let stock = parseInt($(this).val() || 0);
                let stock_actual = parseInt($("#stock_actual").val() || 0);
                let stock_total = stock + stock_actual;
                $("#stock_total").val(stock_total);
            });

            // Sincronización de monedas
            $("#id_moneda").change(function () {
                $(".id_moneda_venta").val($(this).val());
            });

            /*begin: upload-file*/
            $("#file_product").change(function () {
                var input = this;
                var url = $(this).val();
                var ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();

                /*$('#img_product_value').attr('src', _url_cdn + 'SIFAFEL/img/modules/inventory/load-gif.gif');*/

                if (input.files && input.files[0] && (ext == "gif" || ext == "png" || ext == "jpeg" || ext == "jpg")) {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        $('#img_producto').attr('src', e.target.result);

                        //$("#img_producto").val(e.target.result);
                    }
                    reader.readAsDataURL(input.files[0]);
                }
                else {
                    //$('#img_product_value').attr('src', _url_cdn + 'SIFAFEL/img/modules/inventory/load-gif.gif');
                }
            });


            $(".container-image .hover").click(function () {
                $("#file_product").click();
            });

            // Configurar fecha actual
            setTodayDateFormat("#fecha_compra");

            // Configurar Select2 para dropdowns
            $(".form-select").select2({
                //placeholder: "Seleccionar",
                //allowClear: true
            });

            // Configurar focus inicial
            setTimeout(function () {
                $("#codigo").focus();
            }, 1000);

            // Validación de código de barras
            $("#codigo").on("keyup", function () {
                $("#lbl_barcode").html($(this).val());
            });

            // Búsqueda de producto por código al presionar Enter
            $("#codigo").on('keypress', function (e) {
                if (e.which == 13) {
                    if ($(this).val().trim() != "") {
                        _findProduct($("#codigo").val());
                    }
                }
            });

            // Búsqueda de producto por botón
            $("#btnBusProCod").on("click", function () {
                _findProduct($("#codigo").val());
            });

            // Validación de dependencia marca-modelo
            $("#id_marca").change(function () {
                // Limpiar modelos cuando cambia la marca
                $("#id_modelo").empty().append('<option value="">Seleccionar modelo</option>');
            });




            $("#btnGuardaProducto").on("click", function () {
                //registrarProducto();





                //validar formulario
                if ($("#frm-save-product").valid({ lang: 'es' })) {
                    var $inputs = $("#frm-save-product").find(':input');
                    //var _data = {};
                    var _data = new FormData();
                    $inputs.each(function (index) {
                        let data_value = $(this).val();
                        if ($(this).attr('type') == "file") {
                            var file = $(this)[0];
                            if (file.files && file.files[0]) {
                                data_value = file.files[0];
                            }
                        }
                        _data.append($(this).attr('name'), data_value);

                        console.log("va a ir una image");
                        console.log(data_value);
                    });

                    //obtenemos la imagen y la seteamos
                    var srcImagen = $("#img_producto").attr("src");
                    _data.append($("#img_producto").attr('name'), srcImagen);

                    console.log("va a ir una image2");
                    console.log(srcImagen);

                    $.ajax({
                        url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Modulo/Producto/CrearProducto",
                    data: _data,
                    type: "POST",
                    //contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    contentType: false,
                    processData: false,
                    dataType: "json",
                    beforeSend: function () {
                        Holdon_Open("Procesando...");
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            Swal.fire({
                                icon: result.type,
                                text: result.text,
                            });
                        }
                        else {
                            Swal.fire({
                                icon: result.type,
                                text: result.text,
                            });
                        }
                        limpiarCampos();
                    },
                    error: function (result) {
                        console.log('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrió un error durante el proceso',
                        });
                    },
                    complete: function () {
                        Holdon_Close();
                    }
                });
            }
            return false;
        });
        });

        //se agregar los datos de los productos
        function setDataProduct(data_value) {
            $("#id_producto").val(data_value.id_producto);
            $("#codigo").attr("search", "1").attr("data-valor", mtdEnc($("#codigo").val()));
            $("#nombre").val(data_value.nombre);
            $("#descripcion").val(data_value.descripcion);
            $("#img_producto").attr("src", data_value.img_producto || "data:image/png;base64,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");

            $("#id_proveedor").val(data_value.id_proveedor || "").change();
            $("#id_categoria").val(data_value.id_categoria || "").change();
            $("#id_marca").val(data_value.id_marca || "").change();
            $("#id_modelo").val("").change();
            $("#id_modelo").data("value", mtdEnc(data_value.id_modelo || ""));
            $("#anio").val(data_value.anio || "").change();

            $("#fecha_vencimiento").val(DateFormatYYYmmdd(data_value.fecha_vencimientoFD));
            $("#costo_unitario").val(data_value.costo_unitario);
            $("#precio_unitario").val(data_value.precio_unitario);
            $("#min_descuento").val(data_value.min_descuento);
            $("#ganancia_producto").val((data_value.precio_unitario - data_value.costo_unitario).toFixed(2));

            $("#stock_actual").val(data_value.stock_actual);
            $("#stock_maximo").val(data_value.stock_maximo);
            $("#stock_minimo").val(data_value.stock_minimo);
        }

        function limpiarCampos() {
            $("#id_producto").val("0");
            $("#codigo").val("").attr("search", "0").attr("data-valor", "");
            $("#nombre").val("");
            $("#descripcion").val("");
            $("#img_producto").attr("src", "data:image/png;base64,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");

            $("#id_proveedor").val("").change();
            $("#id_categoria").val("").change();
            $("#id_marca").val("").change();
            $("#id_modelo").val("").change();
            $("#anio").val("").change();

            $("#fecha_vencimiento").val("");
            $("#costo_unitario").val(0);
            $("#precio_unitario").val(0);
            $("#min_descuento").val(0);
            $("#ganancia_producto").val(0);

            $("#stock").val(0);
            $("#stock_total").val(0);
            $("#stock_actual").val(0);
            $("#stock_maximo").val(0);
            $("#stock_minimo").val(0);
        }



        // Funciones auxiliares que faltan
        function setTodayDateFormat(selector) {
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            $(selector).val(yyyy + '-' + mm + '-' + dd);
        }

        function DateFormatYYYmmdd(dateString) {
            if (!dateString) return "";
            var date = new Date(dateString);
            var dd = String(date.getDate()).padStart(2, '0');
            var mm = String(date.getMonth() + 1).padStart(2, '0');
            var yyyy = date.getFullYear();
            return yyyy + '-' + mm + '-' + dd;
        }

        function mtdEnc(value) {
            // Función de codificación - implementar según necesidades del proyecto
            return btoa(value);
        }

        function mtdValue(encodedValue) {
            // Función de decodificación - implementar según necesidades del proyecto
            try {
                return atob(encodedValue);
            } catch (e) {
                return encodedValue;
            }
        }

    </script>


</asp:Content>
