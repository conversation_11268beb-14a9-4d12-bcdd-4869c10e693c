<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="100px" height="100px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<g transform="rotate(0 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#6c5ffc">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-1.3043478260869565s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(36 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#05c3fb">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-1.1594202898550725s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(72 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#6c5ffc">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-1.0144927536231882s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(108 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#05c3fb">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.8695652173913043s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(144 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#6c5ffc">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.7246376811594203s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(180 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#05c3fb">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.5797101449275363s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(216 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#6c5ffc">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.43478260869565216s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(252 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#05c3fb">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.2898550724637681s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(288 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#6c5ffc">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="-0.14492753623188406s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(324 50 50)">
  <rect x="41.5" y="24.5" rx="8.5" ry="2.5" width="17" height="5" fill="#05c3fb">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.4492753623188404s" begin="0s" repeatCount="indefinite"></animate>
  </rect>
</g>
<!-- [ldio] generated by https://loading.io/ --></svg>